# tools.py

import os
import sys
import uuid
import pickle
import json
import traceback
from io import String<PERSON>
from datetime import datetime
from typing import Annotated, Optional, Dict, Any, List

import pandas as pd

from langchain_core.tools import tool
from langchain_core.tools.base import InjectedToolCallId
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import ToolMessage

from parser_graph import run_generic_parser_graph
from sandbox_client import SandboxClient
from langgraph.types import Command
# TOOL: Data Retriever

@tool("data_retriever")
def data_retriever(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: RunnableConfig,
    description: Optional[str] = None,
    dataset_name: Optional[str] = None
) -> dict:
    '''
    Retrieves data based on a description or dataset name by querying an external source.

    :param tool_call_id: Injected tool call ID
    :param config: Runnable configuration
    :param description: Description of the data to retrieve
    :param dataset_name: Name of the dataset to retrieve
    '''
    # Get session_id and partner from config
    session_id = config.get("configurable", {}).get("session_id")
    partner = config.get("configurable", {}).get("partner", "oksigen")  # Default to "oksigen" if not specified

    if not session_id:
        raise ValueError("Session ID is required in config for data_retriever")
    query = description if description else dataset_name
    if not query:
        error_message = "Data retrieval failed: Neither description nor dataset_name provided"
        print(f"[Tool][DataRetriever] Error: {error_message}")
        return {
            "status": "error",
            "error": error_message
        }
    print(f"[Tool][DataRetriever] Attempting to retrieve data for query: '{query}' for partner: '{partner}'")
    try:
        # Pass session_id and partner to run_generic_parser_graph
        response = run_generic_parser_graph(query, session_id=session_id, partner=partner)
        print(f"[Tool][DataRetriever] Received response: {response}")
        # Convert InputData objects to dictionaries
        input_data_serialized = [
            {
                "variable_name": d.variable_name,
                "data_path": d.sandbox_file_name,
                "data_description": d.data_description,
                "sandbox_file_name": d.sandbox_file_name,  # Add sandbox_file_name explicitly
            }
            for d in response.get("input_data", [])
        ]
        return {
            "status": "success",
            "input_data": input_data_serialized,
            "data_description": [query],
            "generic_parser_request": [response.get("generated_request", {})],
            "requests_error": response.get("requests_error", [])
        }
    except Exception as e:
        error_message = f"Data retrieval failed: {str(e)}"
        print(f"[Tool][DataRetriever] Error: {error_message}")
        print(f"[Tool][DataRetriever] Error details: {traceback.format_exc()}")
        return {
            "status": "error",
            "error": error_message,
            "failed_query": query
        }



from pathlib import Path

_PLOT_DIR = Path("images/plotly_figures/pickle")
_PLOT_DIR.mkdir(parents=True, exist_ok=True)

def _debug(msg: str):
    """Centralised debug print – swap out for logging if needed."""
    print(f"[Tool] {msg}")

def _extract_io(config: RunnableConfig) -> tuple[list[dict], dict]:
    """Return (input_data, current_variables) extracted from LangGraph config."""
    try:
        # Debug
        print(f"[_extract_io] Config keys: {list(config.keys())}")
        if "configurable" in config:
            print(f"[_extract_io] Configurable keys: {list(config['configurable'].keys())}")
        
        chan = config.get("configurable", {}).get("__pregel_read", {})
        if hasattr(chan, "args") and len(chan.args) > 1:  # functools.partial
            print("[_extract_io] Found partial in __pregel_read")
            chan = chan.args[1].get("channel_values", {})
        if isinstance(chan, dict):
            input_data = chan.get("input_data", [])
            current_vars = chan.get("current_variables", {})
            print(f"[_extract_io] Found input_data: {len(input_data)} items, current_vars: {len(current_vars)} items")
            return input_data, current_vars
    except Exception as exc:
        _debug(f"config parsing failed: {exc}")
    
    print("[_extract_io] Returning empty lists")
    return [], {}

def _load_csv(path: Path) -> pd.DataFrame:
    """Read a CSV with a one-liner that still gives helpful tracebacks."""
    _debug(f"loading {path}")
    return pd.read_csv(path)

def _auto_load_data(input_data: list[dict]) -> dict[str, pd.DataFrame]:
    """Load datasets declared in input_data or auto-discover *.csv files."""
    variables: dict[str, pd.DataFrame] = {}
    declared = [Path(d["data_path"]) for d in input_data if "data_path" in d]
    discovered = declared or list(Path(".").glob("*.csv"))

    for csv in discovered:
        name = csv.stem
        try:
            variables[name] = _load_csv(csv)
        except Exception as exc:
            _debug(f"failed to load {csv}: {exc}")
    return variables

def _save_figs(figures: list) -> list[str]:
    """Pickle Plotly figures and return saved filenames."""
    names = []
    for fig in figures:
        fname = f"{uuid.uuid4()}.pickle"
        with open(_PLOT_DIR / fname, "wb") as f:
            pickle.dump(fig, f)
        names.append(fname)
    return names

# ── main tool ─────────────────────────────────────────────────────────────────
@tool("complete_python_task")
def complete_python_task(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: RunnableConfig,
    python_code: str,
) -> Command:
    """
    Execute arbitrary user Python code in a sandbox environment.
    Returns a Command to update the state with stdout, image filenames, and enriched current_variables.

    :param tool_call_id: Injected tool call ID
    :param config: Runnable configuration
    :param python_code: Python code to execute
    """
    # Get session_id from config
    session_id = config.get("configurable", {}).get("session_id")
    if not session_id:
        raise ValueError("Session ID is required in config for complete_python_task")

    try:
        import asyncio
        from sandbox_client import SandboxClient

        async def execute_in_sandbox():
            client = SandboxClient()

            # Ensure we have a valid session
            session_info = await client.get_session_info(session_id)
            if not session_info:
                # Create a new session if it doesn't exist
                await client.create_session(session_id)
                session_info = await client.get_session_info(session_id)

            # Debug: Check session variables before executing code
            _debug(f"[Tool] Session variables before execution: {session_info.get('variables', {})}")

            # Debug: List files in the sandbox
            list_files_code = """
import os
print(f"Current directory: {os.getcwd()}")
print(f"Files in current directory: {os.listdir('.')}")
"""
            list_files_result = await client.execute_code(
                session_id=session_id,
                code=list_files_code,
                timeout_seconds=5
            )
            _debug(f"[Tool] Files in sandbox before execution: {list_files_result.get('output', '')}")

            # Execute code in sandbox
            _debug(f"[Tool] Executing code in sandbox session {session_id}")
            sandbox_result = await client.execute_code(
                session_id=session_id,
                code=python_code,
                timeout_seconds=30
            )

            # Debug la sortie complète du sandbox
            print(f"[SANDBOX RESULTS]: {sandbox_result}")

            if isinstance(sandbox_result, dict) and sandbox_result.get("success"):
                # Extract and format variables from the sandbox result
                sandbox_variables = sandbox_result.get("variables", {})
                
                # Create a properly formatted current_variables dictionary
                current_variables = {}
                # Create a copy of items to avoid modifying during iteration
                for var_name, var_info in dict(sandbox_variables).items():
                    if isinstance(var_info, dict) and "type" in var_info:
                        # This is already a formatted DataFrame or other structured data
                        current_variables[var_name] = var_info
                    else:
                        # For other types, create a simple descriptor
                        var_type = "unknown"
                        if isinstance(var_info, str):
                            var_type = "string"
                        elif isinstance(var_info, (int, float)):
                            var_type = type(var_info).__name__
                        elif var_info == "module":  # Common case for imported modules
                            var_type = "module"
                        else:
                            var_type = str(type(var_info))
                            
                        current_variables[var_name] = {
                            "type": var_type,
                            "description": f"{var_type}: {str(var_info)[:100]}" + ("..." if len(str(var_info)) > 100 else "")
                        }
                
                # Format the result
                print(f"[FORMATTED RESULT]: current_variables keys: {list(current_variables.keys())}")
                images = sandbox_result.get("plots", [])
                print("--------------------------------")
                print(f"[IMAGES tool]: {images}")
                print("--------------------------------")

                # Return a Command to update the state
                return Command(
                    update={
                        "output_image_paths": images,  # Update plots list
                        "current_variables": current_variables,  # Update variables
                        "messages": [
                            ToolMessage(
                                content=json.dumps({
                                    "stdout": sandbox_result.get("output", ""),
                                    "intermediate_outputs": [{
                                        "code": python_code,
                                        "output": sandbox_result.get("output", "")
                                    }]
                                }),
                                tool_call_id=tool_call_id
                            )
                        ]
                    }
                )
            else:
                error_msg = "Unknown error"
                if isinstance(sandbox_result, dict):
                    error_msg = sandbox_result.get("error", "Unknown error")
                elif isinstance(sandbox_result, str):
                    error_msg = sandbox_result
                return Command(
                    update={
                        "output_image_paths": [],  # Clear plots on error
                        "current_variables": {},  # Clear variables on error
                        "messages": [
                            ToolMessage(
                                content=json.dumps({
                                    "stdout": f"Error: {error_msg}",
                                    "intermediate_outputs": [{
                                        "code": python_code,
                                        "output": f"Error: {error_msg}"
                                    }]
                                }),
                                tool_call_id=tool_call_id
                            )
                        ]
                    }
                )

        # Run the async function
        return asyncio.run(execute_in_sandbox())

    except Exception as e:
        error_msg = f"Error executing code: {str(e)}"
        return Command(
            update={
                "output_image_paths": [],  # Clear plots on error
                "current_variables": {},  # Clear variables on error
                "messages": [
                    ToolMessage(
                        content=json.dumps({
                            "stdout": error_msg,
                            "intermediate_outputs": [{
                                "code": python_code,
                                "output": error_msg
                            }]
                        }),
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )