import mlflow
from mlflow.tracking import <PERSON><PERSON>low<PERSON><PERSON>
from typing import Optional, Dict, Any
import os

def setup_mlflow(
    tracking_uri: str = "http://localhost:5000",
    experiment_name: str = "LangGraph",
    run_name: Optional[str] = None
) -> None:
    """
    Set up MLflow tracking with the specified configuration.
    
    Args:
        tracking_uri: The MLflow tracking server URI
        experiment_name: Name of the MLflow experiment
        run_name: Optional name for the MLflow run
    """
    # Set the tracking URI
    mlflow.set_tracking_uri(tracking_uri)
    
    # Set the experiment
    mlflow.set_experiment(experiment_name)
    
    # Start a new run if run_name is provided
    if run_name:
        mlflow.start_run(run_name=run_name)
    else:
        mlflow.start_run()

def log_parameters(params: Dict[str, Any]) -> None:
    """
    Log parameters to MLflow.
    
    Args:
        params: Dictionary of parameters to log
    """
    mlflow.log_params(params)

def log_metrics(metrics: Dict[str, float]) -> None:
    """
    Log metrics to MLflow.
    
    Args:
        metrics: Dictionary of metrics to log
    """
    mlflow.log_metrics(metrics)

def log_artifact(local_path: str, artifact_path: Optional[str] = None) -> None:
    """
    Log an artifact to MLflow.
    
    Args:
        local_path: Local path to the artifact
        artifact_path: Optional path within the artifact directory
    """
    mlflow.log_artifact(local_path, artifact_path)

def end_run() -> None:
    """End the current MLflow run."""
    mlflow.end_run()

# Enable LangChain autologging
mlflow.langchain.autolog() 