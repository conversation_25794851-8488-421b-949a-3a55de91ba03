oksigen:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids
        (e.g., electric consumption, water, etc.).
      mode: in
      indicator_options:
        energie_facturante_grdf: "to get the electric consumption data of 'grdf' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'enedis' meters"
    - id: load_curve
      description: |
        Uses the load curve service to get the detailed time series of
        electric consumption data for a few or one site at high granularity.
      mode: out
      indicator_options:
        total-power_supply: "to get time series of the total power used by the site for each timestamp"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
ceasaclay:
  algorithms: []
