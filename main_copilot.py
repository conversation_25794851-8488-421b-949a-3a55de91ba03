import os
import uuid
import json
import asyncio
from typing import Dict, List, Any, Optional, TypedDict, Annotated
from dataclasses import dataclass
from operator import or_, add

# <PERSON><PERSON><PERSON><PERSON> and LangGraph imports
from langchain_core.messages import SystemMessage, HumanMessage, ToolMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.messages.utils import count_tokens_approximately
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph.message import add_messages

# Model and tool imports
from model import model, summary_model, get_prompt_for_partner
from tools import complete_python_task, data_retriever
from summary import pre_model_hook, ensure_message_ids, remove_duplicate_messages


# Database config (use environment variables)
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_CHECKPOINTER = (
    f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?sslmode=disable"
)

# ─────────────────────────────────────────────────────────────────────────────
# Data structures
@dataclass
class InputData:
    variable_name: str
    data_path: str
    data_description: str
    sandbox_file_name: str
    def __hash__(self):
        return hash((self.variable_name, self.data_path, self.data_description,self.sandbox_file_name))

class AgentState(TypedDict):
    messages: Annotated[List[Any], add_messages]
    remaining_steps: int
    input_data: Annotated[List[InputData], add]
    intermediate_outputs: Annotated[List[dict], add]
    current_variables: Annotated[Dict[str, Any], or_]
    output_image_paths: Annotated[List[str], add]
    data_description: Annotated[List[str], add]
    generic_parser_request: Annotated[List[Any], add]
    conversation_id: str
    session_id: str
    partner: str
    partner_config: Optional[Dict[str, Any]]
    summary: str
    id_last_summary: Optional[str]

# ─────────────────────────────────────────────────────────────────────────────
# Utility: shallow merge of two dicts

def _merge(a: Dict[str, Any], b: Dict[str, Any]) -> Dict[str, Any]:
    return {**a, **b}

# ─────────────────────────────────────────────────────────────────────────────
# Checkpointer factory

def make_postgres_checkpointer():
    """
    Return an async context manager for AsyncPostgresSaver.
    Usage:
        async with make_postgres_checkpointer() as ckpt:
            ...
    """
    return AsyncPostgresSaver.from_conn_string(DB_URI_CHECKPOINTER)

tools = [data_retriever, complete_python_task]
# ─────────────────────────────────────────────────────────────────────────────
# Agent factory

def create_agent(checkpointer, partner: str):
    """
    Return a LangGraph React agent with the given async checkpointer.
    """
    prompt = ChatPromptTemplate.from_messages([
        ("system", get_prompt_for_partner()),
        ("placeholder", "{messages}"),
    ])

    agent = create_react_agent(
        model=model,
        tools=tools,
        prompt=prompt,
        pre_model_hook=pre_model_hook,
        state_schema=AgentState,
        checkpointer=checkpointer,
    )
    return agent

# ─────────────────────────────────────────────────────────────────────────────
# Async REPL with streaming

async def interactive(thread_id: Optional[str] = None, partner: str = "oksigen") -> None:
    if thread_id is None:
        thread_id = str(uuid.uuid4())
    print(f"Energy Data Copilot – Thread ID: {thread_id}, Partner: {partner}")

    # create async checkpointer and agent
    async with make_postgres_checkpointer() as ckpt:
        agent = create_agent(ckpt, partner)

        # initial state
        state: AgentState = {
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner,
            "partner_config": {},
            "messages": [],
            "remaining_steps": 10,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "id_last_summary": None,
            "summary": "",
        }

        config = {"configurable": {"thread_id": thread_id,
                                  "session_id": thread_id,
                                  "partner": partner}}

        # REPL loop
        while True:
            try:
                user_input = input("\n[User]: ")
            except (EOFError, KeyboardInterrupt):
                print("\nExiting – conversation ID:", thread_id)
                return
            if user_input.strip().lower() == "exit":
                print("Good-bye!")
                return

            state["messages"].append(HumanMessage(content=user_input))
            print(f"[Debug] invoking agent – {len(state['messages'])} messages")

            last_chunk = None
            async for chunk in agent.astream({"messages": state["messages"]}, config, stream_mode="updates"):
                last_chunk = chunk
                # incremental assistant output
                if chunk.get("messages"):
                    msg = chunk["messages"][-1]
                    if hasattr(msg, "content") and msg.content:
                        print(msg.content, end="", flush=True)

                # harvest tool outputs
                for m in chunk.get("messages", []):
                    if isinstance(m, ToolMessage):
                        try:
                            payload = m.content if isinstance(m.content, dict) else json.loads(m.content)
                        except Exception:
                            continue
                        if payload.get("input_data"):
                            state["input_data"].extend(payload["input_data"])
                        if payload.get("current_variables"):
                            state["current_variables"] = _merge(
                                state["current_variables"],
                                payload["current_variables"],
                            )
            print()

            # update only messages slice
            if last_chunk and "messages" in last_chunk:
                state["messages"] = last_chunk["messages"]

            # dedupe context
            msgs = ensure_message_ids(state["messages"])
            unique, removed = remove_duplicate_messages(msgs)
            if removed:
                state["messages"] = unique
                print(f"[Debug] deduplicated {len(removed)} msgs")

# ─────────────────────────────────────────────────────────────────────────────
# Entry-point
if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Energy Data Copilot (CLI Mode)")
    parser.add_argument("--thread-id", type=str, help="Thread ID to resume")
    parser.add_argument("--partner", type=str, default="oksigen", help="Partner code")
    args = parser.parse_args()

    asyncio.run(interactive(thread_id=args.thread_id, partner=args.partner))
